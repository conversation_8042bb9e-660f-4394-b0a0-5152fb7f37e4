@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@300;400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   BAKASANA - Ultra Minimalist Luxury Design System
   Inspired by: Apple, Linear.app, Kinfolk, High-end Spa Resorts
   Philosophy: 80% white space, typography as decoration, zero ornaments
   ========================================================================== */

@layer base {
  :root {
    /* LUXURY COLOR PALETTE - Warm Minimalism */
    --color-primary: 44 44 44;         /* Deep Charcoal (#2C2C2C) */
    --color-secondary: 254 253 248;    /* Warm White (#FEFDF8) */
    --color-accent: 124 152 133;       /* Sage Green (#7C9885) - kept as requested */
    --color-background: 254 253 248;   /* Warm White Background */
    --color-text-light: 102 102 102;   /* Light text (#666) */

    /* PREMIUM TYPOGRAPHY - Editorial Quality */
    --font-sans: 'Source Sans Pro', 'system-ui', '-apple-system', 'BlinkMacSystemFont', sans-serif;
    --font-serif: 'Playfair Display', 'Georgia', 'Times New Roman', serif;
    
    /* ULTRA-SUBTLE SHADOWS - Barely Visible */
    --shadow-soft: 0 1px 3px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 2px 6px rgba(0, 0, 0, 0.08);

    /* PREMIUM TIMING FUNCTIONS */
    --ease-gentle: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    font-family: var(--font-sans);
    background: rgb(var(--color-background));
    color: rgb(var(--color-primary));
    letter-spacing: 0.01em;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* PREMIUM TYPOGRAPHY - Large, breathing, hierarchical */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-serif);
    color: rgb(var(--color-primary));
    font-weight: 400;
    letter-spacing: 0.02em;
    line-height: 1.2;
  }

  h1 {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 300;
    letter-spacing: 0.02em;
    line-height: 1.1;
    margin-bottom: 2rem;
  }

  h2 {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.2;
    margin-bottom: 1.5rem;
  }

  h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 400;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  /* Body text - Premium spacing and readability */
  p {
    font-size: clamp(1rem, 1.5vw, 1.125rem);
    color: rgb(var(--color-text-light));
    margin-bottom: 2rem;
    font-weight: 300;
    line-height: 1.8;
    max-width: 65ch;
  }

  p:last-child {
    margin-bottom: 0;
  }

  a {
    color: rgb(var(--color-accent));
    text-decoration: none;
    font-weight: 300;
    transition: opacity 0.3s var(--ease-smooth);
  }

  a:hover {
    opacity: 0.7;
  }

  ::selection {
    color: rgb(var(--color-primary));
    background-color: rgb(var(--color-accent) / 0.1);
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
    image-rendering: -webkit-optimize-contrast;
  }

  /* Minimal fade-in animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in {
    animation: fadeIn 0.8s var(--ease-gentle) forwards;
  }

  /* Scrollbar styling - Minimal */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 9999px;
    background-color: rgb(var(--color-accent) / 0.20);
    transition: background-color 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgb(var(--color-accent) / 0.40);
  }
}

@layer components {
  /* HERO SECTION - Clean and Elegant */
  .hero-section {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: rgb(var(--color-secondary));
  }

  .hero-content {
    position: relative;
    z-index: 10;
    text-align: center;
    max-width: 64rem;
    margin: 0 auto;
    padding: 0 2rem;
  }

  .hero-title {
    font-size: clamp(4rem, 8vw, 6rem);
    font-family: var(--font-serif);
    font-weight: 300;
    margin-bottom: 2rem;
    letter-spacing: 0.02em;
    color: rgb(var(--color-primary));
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem);
    margin-bottom: 3rem;
    color: rgb(var(--color-primary) / 0.85);
    font-weight: 300;
    max-width: 36rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* PREMIUM BUTTONS - Ghost Style */
  .btn-primary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-primary) / 0.3);
    color: rgb(var(--color-primary));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-primary));
  }

  .btn-secondary {
    padding: 1rem 2rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: transparent;
    border: 1px solid rgb(var(--color-accent) / 0.3);
    color: rgb(var(--color-accent));
    transition: all 0.3s var(--ease-smooth);
    font-weight: 300;
    border-radius: 0;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
    border-color: rgb(var(--color-accent));
  }

  /* ULTRA MINIMAL NAVIGATION */
  .navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(254, 253, 248, 0.98);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    z-index: 50;
    transition: all 0.3s var(--ease-smooth);
  }

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    max-width: 75rem;
    margin: 0 auto;
  }

  .nav-link {
    padding: 0.75rem 1.5rem;
    color: rgb(var(--color-primary) / 0.7);
    font-weight: 300;
    transition: opacity 0.3s var(--ease-smooth);
  }

  .nav-link:hover {
    opacity: 0.7;
  }

  .nav-link[aria-current="page"] {
    color: rgb(var(--color-accent));
  }

  /* PREMIUM CARDS */
  .card {
    background: transparent;
    border-radius: 0;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--shadow-soft);
    transition: all 0.3s var(--ease-smooth);
    overflow: hidden;
  }

  .card:hover {
    border-color: rgba(0, 0, 0, 0.08);
    box-shadow: var(--shadow-medium);
  }

  .card-content {
    padding: 2.5rem;
  }

  .card-image {
    position: relative;
    overflow: hidden;
    aspect-ratio: 16/9;
  }

  .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

@layer utilities {
  /* ULTRA-PREMIUM LAYOUT - Lots of White Space */
  .section-padding {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  @media (min-width: 768px) {
    .section-padding {
      padding-top: 10rem;
      padding-bottom: 10rem;
    }
  }

  .container-unified {
    max-width: 75rem;
    margin-left: auto;
    margin-right: auto;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .section-unified-title {
    text-align: center;
    margin-bottom: 6rem;
  }

  .section-unified-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-accent));
  }

  .section-unified-title p {
    font-size: 1.125rem;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.85);
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
  }

  /* PREMIUM GRID SYSTEM */
  .grid-12-col {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.5rem;
  }

  @media (min-width: 768px) {
    .grid-12-col {
      gap: 2rem;
    }
  }

  .main-content {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .main-content {
      grid-column: span 8;
    }
  }

  .sidebar {
    grid-column: span 12;
  }

  @media (min-width: 768px) {
    .sidebar {
      grid-column: span 4;
    }
  }

  /* SECTION STYLING */
  .section {
    position: relative;
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  @media (min-width: 1024px) {
    .section {
      padding-top: 8rem;
      padding-bottom: 8rem;
    }
  }

  .section-title {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-title h2 {
    margin-bottom: 1.5rem;
    color: rgb(var(--color-primary));
  }

  .section-title p {
    max-width: 48rem;
    margin-left: auto;
    margin-right: auto;
    font-size: 1.125rem;
    color: rgb(var(--color-primary) / 0.7);
  }

  /* MINIMAL DECORATIVE ELEMENTS */
  .divider-line {
    height: 1px;
    background: rgb(var(--color-accent) / 0.1);
    margin: 6rem auto;
    max-width: 80px;
  }

  .section-number {
    position: absolute;
    top: 2rem;
    left: 2rem;
    font-size: clamp(4rem, 8vw, 8rem);
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.02);
    font-family: var(--font-serif);
    line-height: 1;
    z-index: 0;
    pointer-events: none;
  }

  /* UTILITY CLASSES */
  .meta-text {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2em;
    font-weight: 300;
    color: rgb(var(--color-primary) / 0.6);
  }

  .subtitle {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.15em;
    color: rgb(var(--color-accent) / 0.7);
    font-weight: 300;
  }

  /* RESPONSIVE DESIGN */
  @media (max-width: 768px) {
    .hero-section {
      min-height: 85vh;
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    .card-content {
      padding: 1.5rem;
    }

    .nav-content {
      padding: 1rem;
    }

    .container-unified {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  /* ACCESSIBILITY */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .fade-in {
      animation: none !important;
    }
  }

  /* PRINT STYLES */
  @media print {
    * {
      box-shadow: none !important;
      text-shadow: none !important;
      background: transparent !important;
      color: black !important;
    }

    .navbar,
    .btn-primary,
    .btn-secondary {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
      font-size: 12pt;
      line-height: 1.4;
    }

    a,
    a:visited {
      text-decoration: underline;
      color: #444 !important;
    }

    img {
      max-width: 100% !important;
      page-break-inside: avoid;
    }

    h2, h3 {
      page-break-after: avoid;
    }

    .card {
      border: 1px solid #ddd !important;
      page-break-inside: avoid;
    }
  }
}
